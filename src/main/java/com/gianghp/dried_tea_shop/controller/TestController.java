package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import com.gianghp.dried_tea_shop.common.response.ApiResponse;
import com.gianghp.dried_tea_shop.exception.BusinessException;
import com.gianghp.dried_tea_shop.exception.NotFoundException;
import com.gianghp.dried_tea_shop.exception.ValidationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * Test controller để demo các thành phần đã tạo
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/test")
public class TestController {
    
    /**
     * Test success response
     */
    @GetMapping("/success")
    public ResponseEntity<ApiResponse<String>> testSuccess() {
        return ResponseEntity.ok(ApiResponse.success("Test success response"));
    }
    
    /**
     * Test success response with data
     */
    @GetMapping("/success-with-data")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testSuccessWithData() {
        Map<String, Object> data = new HashMap<>();
        data.put("message", "Hello World");
        data.put("timestamp", System.currentTimeMillis());
        data.put("version", "1.0.0");
        
        return ResponseEntity.ok(ApiResponse.success(data, "Data retrieved successfully"));
    }
    
    /**
     * Test BusinessException
     */
    @GetMapping("/business-error")
    public ResponseEntity<ApiResponse<String>> testBusinessError() {
        throw new BusinessException(ErrorCode.BUSINESS_ERROR, "This is a test business error");
    }
    
    /**
     * Test NotFoundException
     */
    @GetMapping("/not-found-error")
    public ResponseEntity<ApiResponse<String>> testNotFoundError() {
        throw new NotFoundException("User", 123L);
    }
    
    /**
     * Test ValidationException
     */
    @GetMapping("/validation-error")
    public ResponseEntity<ApiResponse<String>> testValidationError() {
        Map<String, String> errors = new HashMap<>();
        errors.put("email", "Invalid email format");
        errors.put("password", "Password is too weak");
        
        throw new ValidationException(errors);
    }
    
    /**
     * Test method argument validation
     */
    @PostMapping("/validate")
    public ResponseEntity<ApiResponse<String>> testValidation(@Valid @RequestBody TestRequest request) {
        return ResponseEntity.ok(ApiResponse.success("Validation passed"));
    }
    
    /**
     * Test generic exception
     */
    @GetMapping("/generic-error")
    public ResponseEntity<ApiResponse<String>> testGenericError() {
        throw new RuntimeException("This is a test runtime exception");
    }
    
    /**
     * Test slow method (for logging aspect)
     */
    @GetMapping("/slow")
    public ResponseEntity<ApiResponse<String>> testSlowMethod() throws InterruptedException {
        Thread.sleep(2000); // Sleep 2 seconds
        return ResponseEntity.ok(ApiResponse.success("Slow method completed"));
    }
    
    /**
     * Test request with parameters
     */
    @GetMapping("/with-params")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testWithParams(
            @RequestParam String name,
            @RequestParam(required = false) Integer age,
            @RequestParam(defaultValue = "USER") String role) {
        
        Map<String, Object> data = new HashMap<>();
        data.put("name", name);
        data.put("age", age);
        data.put("role", role);
        
        return ResponseEntity.ok(ApiResponse.success(data, "Parameters received"));
    }
    
    /**
     * Test DTO for validation
     */
    public static class TestRequest {
        @NotBlank(message = "Name is required")
        private String name;
        
        @NotNull(message = "Age is required")
        private Integer age;
        
        // Getters and setters
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public Integer getAge() {
            return age;
        }
        
        public void setAge(Integer age) {
            this.age = age;
        }
    }
}
