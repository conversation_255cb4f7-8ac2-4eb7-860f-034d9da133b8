package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.Product;
import com.gianghp.dried_tea_shop.enums.ProductStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Repository
public interface ProductRepository extends JpaRepository<Product, UUID>,
    JpaSpecificationExecutor<Product> {
    
    /**
     * Find products by status
     */
    List<Product> findByStatus(ProductStatus status);
    
    /**
     * Find products by status with pagination
     */
    Page<Product> findByStatus(ProductStatus status, Pageable pageable);
    
    /**
     * Find products by category
     */
    List<Product> findByCategoryId(UUID categoryId);
    
    /**
     * Find products by category with pagination
     */
    Page<Product> findByCategoryId(UUID categoryId, Pageable pageable);
    
    /**
     * Find products by category and status
     */
    List<Product> findByCategoryIdAndStatus(UUID categoryId, ProductStatus status);
    
    /**
     * Find products by name containing (case insensitive)
     */
    List<Product> findByNameContainingIgnoreCase(String name);
    
    /**
     * Find products by name containing and status
     */
    List<Product> findByNameContainingIgnoreCaseAndStatus(String name, ProductStatus status);
    
    /**
     * Find products by price range
     */
    List<Product> findByPriceBetween(BigDecimal minPrice, BigDecimal maxPrice);
    
    /**
     * Find products by price range and status
     */
    List<Product> findByPriceBetweenAndStatus(BigDecimal minPrice, BigDecimal maxPrice, ProductStatus status);
    
    /**
     * Find products with stock quantity greater than
     */
    List<Product> findByStockQuantityGreaterThan(Integer quantity);
    
    /**
     * Find products with low stock (less than specified quantity)
     */
    List<Product> findByStockQuantityLessThan(Integer quantity);
    
    /**
     * Find products with no stock
     */
    List<Product> findByStockQuantity(Integer quantity);
    
    /**
     * Find products by rating greater than or equal
     */
    List<Product> findByRatingGreaterThanEqual(BigDecimal rating);

    /**
     * Count products by status
     */
    long countByStatus(ProductStatus status);

    /**
     * Count products by category
     */
    long countByCategoryId(UUID categoryId);

    // Removed complex queries - will be handled by service layer if needed
}
