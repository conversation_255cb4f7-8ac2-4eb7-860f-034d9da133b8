package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.Category;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface CategoryRepository extends JpaRepository<Category, UUID> {
    
    /**
     * Find category by name
     */
    Optional<Category> findByName(String name);
    
    /**
     * Check if name exists
     */
    boolean existsByName(String name);
    
    /**
     * Find categories by name containing (case insensitive)
     */
    List<Category> findByNameContainingIgnoreCase(String name);
    
    /**
     * Find categories by description containing (case insensitive)
     */
    List<Category> findByDescriptionContainingIgnoreCase(String description);

    // Removed complex queries - will be handled by service layer if needed
}
