spring.application.name=dried-tea-shop

# Port ch?y production
server.port=8080

# Encoding
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# ?n thông tin l?i chi ti?t (production)
server.error.include-message=never
server.error.include-binding-errors=never

# Database (NeonDB Cloud)
spring.datasource.url=***********************************************************************************************************************************************************************
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Logging (ít verbose h?n)
logging.level.root=INFO
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.level.com.gianghp.dried_tea_shop=INFO

# Validation
spring.mvc.throw-exception-if-no-handler-found=true
spring.web.resources.add-mappings=false

# Swagger (b?n có th? disable h?n trong prod n?u mu?n)
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false
